# News Summarizer

Eine Next.js-Web-App, die Artikel von URLs crawlt und sie im Stil von Morning Brew zusammenfasst.

## Features

- URL-basiertes Artikel-Crawling
- Zusammenfassung mit OpenAI im Morning Brew-Stil
- Klare, verständliche Artikel mit maximal 350 Wörtern
- Glossar mit Fachbegriff-Erklärungen
- Responsive Design mit Tailwind CSS

## Installation

1. Dependencies installieren:
```bash
npm install
```

2. Umgebungsvariablen einrichten:
   - Kopieren Sie `.env.example` zu `.env.local`
   - Fügen Sie Ihren OpenAI API-Key ein:
   ```
   OPENAI_API_KEY=your-actual-api-key
   ```

3. Entwicklungsserver starten:
```bash
npm run dev
```

4. Öffnen Sie [http://localhost:3000](http://localhost:3000) in Ihrem Browser

## Verwendung

1. Geben Sie eine Artikel-URL in das Eingabefeld ein
2. <PERSON><PERSON><PERSON> Sie auf "Zusammenfassen"
3. Der zusammengefasste Artikel wird unterhalb angezeigt

## Technischer Stack

- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes
- **AI**: OpenAI API (GPT-4)
- **Web Scraping**: Cheerio, Axios

## Artikel-Format

Die zusammengefassten Artikel folgen diesem Format:
- **Titel**: Max. 60 Zeichen
- **Struktur**: Was ist passiert? → Warum ist das relevant? → Größerer Zusammenhang
- **Stil**: Klar, sachlich, leicht pointiert
- **Glossar**: Fachbegriffe werden am Ende erklärt

## Entwicklung

Für die Weiterentwicklung:
- API-Route: `/app/api/summarize/route.ts`
- Frontend: `/app/page.tsx`
- Styles: `/app/globals.css`
