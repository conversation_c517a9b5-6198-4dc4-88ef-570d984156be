@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Prose Styles */
.prose h1 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.prose h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
}

.prose p {
  margin-bottom: 1rem;
  line-height: 1.6;
}

.prose strong {
  font-weight: 700;
  color: #1e40af;
}

/* Glossar Box */
.glossar {
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-top: 2rem;
}

.glossar h2 {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #374151;
}

.glossar p {
  margin-bottom: 0.75rem;
  line-height: 1.5;
}

.glossar p:last-child {
  margin-bottom: 0;
}

.glossar strong {
  color: #1f2937;
  font-weight: 600;
}

/* Verbesserte Stile für die Textauswahl */
::selection {
  background-color: rgba(59, 130, 246, 0.4);
  color: inherit;
}

/* Stelle sicher, dass die Auswahl sichtbar bleibt */
.prose ::selection {
  background-color: rgba(59, 130, 246, 0.4);
  color: inherit;
}

/* Animation für die Erklärungsbox */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.prose .explanation-box {
  animation: fadeIn 0.3s ease-out;
}
