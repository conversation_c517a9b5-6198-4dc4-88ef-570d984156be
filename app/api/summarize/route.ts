import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';
import axios from 'axios';
import * as cheerio from 'cheerio';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

async function fetchArticleContent(url: string): Promise<string> {
  try {
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });
    
    const $ = cheerio.load(response.data);
    
    // Remove script and style elements
    $('script, style').remove();
    
    // Try to find article content in common selectors
    let content = '';
    const contentSelectors = [
      'article',
      'main',
      '[role="main"]',
      '.article-content',
      '.post-content',
      '.entry-content',
      '.content',
      '#content'
    ];
    
    for (const selector of contentSelectors) {
      const element = $(selector);
      if (element.length > 0) {
        content = element.text();
        break;
      }
    }
    
    // Fallback to body if no specific content area found
    if (!content) {
      content = $('body').text();
    }
    
    // Clean up the text
    content = content
      .replace(/\s+/g, ' ')
      .replace(/\n{3,}/g, '\n\n')
      .trim();
    
    return content;
  } catch (error) {
    throw new Error('Fehler beim Abrufen des Artikelinhalts');
  }
}

async function extractRelevantText(content: string): Promise<string> {
  console.log("\n\n==== ORIGINAL CONTENT ====");
  console.log("Länge:", content.length, "Zeichen");
  console.log("Vorschau:", content.substring(0, 200) + "...");
  console.log("==========================\n\n");
  
  try {
    // LLM-Anfrage zur Extraktion des relevanten Textes
    const extractionCompletion = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: "Du bist ein Assistent, der relevante Inhalte aus Webseiten-Text extrahiert. Entferne Navigationsmenüs, Werbung, Footers, Kommentare und andere irrelevante Elemente. Gib nur den Haupttext des Artikels zurück."
        },
        {
          role: "user",
          content: `Extrahiere den relevanten Artikeltext aus folgendem Webseiten-Inhalt. Gib NUR den relevanten Text zurück, ohne Erklärungen oder Kommentare:\n\n${content}`
        }
      ],
      temperature: 0.3,
      max_tokens: 1000,
    });
    
    const relevantText = extractionCompletion.choices[0]?.message?.content || content;
    
    console.log("\n\n==== EXTRACTED CONTENT ====");
    console.log("Länge:", relevantText.length, "Zeichen");
    console.log("Vorschau:", relevantText.substring(0, 200) + "...");
    console.log("============================\n\n");
    
    return relevantText;
  } catch (error) {
    console.error("\n\n==== EXTRACTION ERROR ====");
    console.error(error);
    console.error("==========================\n\n");
    return content; // Fallback zum ursprünglichen Inhalt bei Fehler
  }
}

async function checkArchiveAndFetchContent(url: string): Promise<string> {
  try {
    // Versuche zuerst, die Seite auf archive.is zu finden
    const archiveUrl = `https://archive.is/${url}`;
    console.log(`Prüfe Archive.is: ${archiveUrl}`);
    
    try {
      const archiveResponse = await axios.get(archiveUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });
      
      const $ = cheerio.load(archiveResponse.data);
      
      // Suche nach dem spezifischen Element
      const archiveLink = $('a[style*="display:block;color:blue;font-size:16px;word-break:break-word"]');
      
      if (archiveLink.length > 0) {
        const archivedPageUrl = archiveLink.attr('href');
        console.log(`Archive.is Link gefunden: ${archivedPageUrl}`);
        
        // Hole den Inhalt von der archivierten Seite
        if (archivedPageUrl) {
          const archivedContent = await fetchArticleContent(archivedPageUrl);
          console.log("Inhalt von archivierter Seite geladen");
          return archivedContent;
        }
      }
      
      console.log("Kein passender Archive.is Link gefunden, verwende Original-URL");
    } catch (archiveError) {
      console.log("Fehler beim Zugriff auf Archive.is:", archiveError.message);
    }
    
    // Fallback: Verwende die Original-URL
    return await fetchArticleContent(url);
  } catch (error) {
    throw new Error('Fehler beim Abrufen des Artikelinhalts');
  }
}

export async function POST(request: NextRequest) {
  try {
    const { url } = await request.json();
    
    if (!url) {
      return NextResponse.json(
        { error: 'URL ist erforderlich' },
        { status: 400 }
      );
    }
    
    // Prüfe Archive.is und hole den Artikelinhalt
    const articleContent = await checkArchiveAndFetchContent(url);
    
    if (!articleContent) {
      return NextResponse.json(
        { error: 'Kein Inhalt gefunden' },
        { status: 400 }
      );
    }
    
    // Extract relevant content
    const relevantContent = await extractRelevantText(articleContent);
    
    // Create the prompt
    const prompt = `Du bist Redakteur eines modernen, verständlich geschriebenen Newsletters im Stil von Morning Brew. Verfasse einen kompakten Artikel mit maximal 350 Wörtern, der ein komplexes Thema leicht verständlich, klar gegliedert und gut lesbar vermittelt.

📌 Stil und Ton: Klar, sachlich, leicht pointiert – niemals trocken. Keine langen verschachtelten Sätze. Schreib für Menschen mit Grundwissen, aber ohne Spezialwissen.

📌 Struktur des Artikels:
- Titel (<h1>) – Max. 60 Zeichen, klar und neugierig machend
- Untertitel (1 Satz – optional) – Kernaussage oder Cliffhanger zur Einleitung
- Abschnitt 1: Was ist passiert? – Wer? Was? Wann? Wo? In 2–3 Sätzen.
- Abschnitt 2: Warum ist das relevant? – Was bedeutet es? Für wen? Welche Konsequenzen?
- Abschnitt 3: Größerer Zusammenhang – Hintergründe, Entwicklung, Zahlen, Zitate – Wichtige Begriffe mit <strong> hervorheben
- Optionaler Abschluss: – Ausblick, Einschätzung oder Verbindung zur Lebensrealität

📌 Glossar (am Ende), dass Komplizierte Fachbegriffe erklärt: Füge am Ende des Artikels ein einfach formuliertes Glossar in einem Kasten hinzu. Verwende die folgende Struktur für das Glossar:
<div class="glossar">
<h2>Glossar</h2>
<p><strong>Fachbegriff 1:</strong> Kurze, laienverständliche Erklärung.</p>
<p><strong>Fachbegriff 2:</strong> ...</p>
</div>

📌 Formatvorgaben:
- Verwende nur: <h1>, <h2>, <strong>, <div class="glossar">, <br>
- Keine Aufzählungslisten im Haupttext (außer absolut sinnvoll)
- Kein überflüssiger Stil – Fokus auf Klarheit
- Wichtige Absätze mit <br><br> trennen, sodass ein Abstand entsteht und der Inhalt besser lesbar ist

✍️ Thema: ${relevantContent}`;

    // Call OpenAI API
    const completion = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: "Du bist ein erfahrener Redakteur für verständliche Newsletter."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 1500,
    });
    
    let summary = completion.choices[0]?.message?.content || '';
    
    /* Debug-Informationen auskommentiert
    summary += `
    
<div style="margin-top: 30px; padding: 15px; background-color: #f8f9fa; border: 1px solid #ddd; border-radius: 5px;">
  <h2>Debug-Informationen</h2>
  <p><strong>Original-Inhalt:</strong> ${articleContent.length} Zeichen</p>
  <p><strong>Relevanter Inhalt:</strong> ${relevantContent.length} Zeichen</p>
  <p><strong>Original-Vorschau:</strong> ${articleContent.substring(0, 100).replace(/</g, '&lt;').replace(/>/g, '&gt;')}...</p>
  <p><strong>Relevante Vorschau:</strong> ${relevantContent.substring(0, 100).replace(/</g, '&lt;').replace(/>/g, '&gt;')}...</p>
</div>`;
    */
    
    return NextResponse.json({ summary });
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json(
      { error: 'Fehler beim Verarbeiten der Anfrage' },
      { status: 500 }
    );
  }
}
