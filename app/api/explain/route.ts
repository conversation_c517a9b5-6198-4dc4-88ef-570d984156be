import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function POST(request: NextRequest) {
  try {
    const { text } = await request.json();
    
    if (!text) {
      return NextResponse.json(
        { error: 'Text ist erforderlich' },
        { status: 400 }
      );
    }
    
    // Call OpenAI API for explanation
    const completion = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: "Du bist ein hilfreicher Assistent, der komplexe Begriffe und Konzepte einfach und verständlich erklärt."
        },
        {
          role: "user",
          content: `Erkläre den folgenden Begriff oder Textabschnitt kurz und verständlich (max. 100 Wörter):\n\n"${text}"`
        }
      ],
      temperature: 0.5,
      max_tokens: 200,
    });
    
    const explanation = completion.choices[0]?.message?.content || 'Keine Erklärung verfügbar.';
    
    return NextResponse.json({ explanation });
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json(
      { error: 'Fehler beim Verarbeiten der Anfrage' },
      { status: 500 }
    );
  }
}