
'use client';

import { useState, useRef } from 'react';

export default function Home() {
  const [url, setUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [article, setArticle] = useState('');
  const [error, setError] = useState('');
  const [explanation, setExplanation] = useState('');
  const [explainLoading, setExplainLoading] = useState(false);
  const [explainInput, setExplainInput] = useState('');
  const articleRef = useRef<HTMLDivElement>(null);

  const handleSummarize = async () => {
    if (!url) {
      setError('Bitte geben Sie eine URL ein');
      return;
    }

    setLoading(true);
    setError('');
    setArticle('');
    setExplanation('');

    try {
      const response = await fetch('/api/summarize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url }),
      });

      if (!response.ok) {
        throw new Error('<PERSON><PERSON> beim <PERSON> des Artikels');
      }

      const data = await response.json();
      setArticle(data.summary);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Ein Fehler ist aufgetreten');
    } finally {
      setLoading(false);
    }
  };

  const handleExplain = async () => {
    if (!explainInput.trim()) return;

    setExplainLoading(true);

    try {
      const response = await fetch('/api/explain', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text: explainInput }),
      });

      if (!response.ok) {
        throw new Error('Fehler beim Erklären des Textes');
      }

      const data = await response.json();
      setExplanation(data.explanation);
    } catch (err) {
      setExplanation('Fehler beim Laden der Erklärung.');
    } finally {
      setExplainLoading(false);
    }
  };



  return (
    <main className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-gray-900 mb-8 text-center">
          News Summarizer
        </h1>
        
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="flex gap-4">
            <input
              type="url"
              placeholder="URL eingeben..."
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={loading}
            />
            <button
              onClick={handleSummarize}
              disabled={loading}
              className="px-6 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              {loading ? 'Wird zusammengefasst...' : 'Zusammenfassen'}
            </button>
          </div>
          
          {error && (
            <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg text-red-700">
              {error}
            </div>
          )}
        </div>

        {article && (
          <div className="bg-white rounded-lg shadow-md p-8" ref={articleRef}>
            <div
              className="prose prose-lg max-w-none"
              dangerouslySetInnerHTML={{ __html: article }}
            />
          </div>
        )}

        {/* Erklärungsbereich */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-bold text-gray-800 mb-4">Text erklären lassen</h2>
          <div className="flex gap-4">
            <input
              type="text"
              placeholder="Text oder Begriff eingeben, den Sie erklärt haben möchten..."
              value={explainInput}
              onChange={(e) => setExplainInput(e.target.value)}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={explainLoading}
              onKeyDown={(e) => e.key === 'Enter' && handleExplain()}
            />
            <button
              onClick={handleExplain}
              disabled={explainLoading || !explainInput.trim()}
              className="px-6 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              {explainLoading ? 'Erkläre...' : 'Erklären'}
            </button>
          </div>

          {explanation && (
            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-bold text-blue-800 mb-2">Erklärung zu: "{explainInput}"</h3>
              <p className="text-gray-800">{explanation}</p>
              <button
                className="mt-2 text-sm text-blue-600 hover:text-blue-800"
                onClick={() => setExplanation('')}
              >
                Schließen
              </button>
            </div>
          )}
        </div>
      </div>
    </main>
  );
}
