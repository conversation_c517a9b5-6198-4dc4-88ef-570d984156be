
'use client';

import { useState, useEffect, useRef } from 'react';

export default function Home() {
  const [url, setUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [article, setArticle] = useState('');
  const [error, setError] = useState('');
  const [selectedText, setSelectedText] = useState('');
  const [explanation, setExplanation] = useState('');
  const [showExplanation, setShowExplanation] = useState(false);
  const [explainLoading, setExplainLoading] = useState(false);
  const [selectionPosition, setSelectionPosition] = useState({ top: 0, left: 0 });
  const articleRef = useRef<HTMLDivElement>(null);
  const [savedRange, setSavedRange] = useState<Range | null>(null);
  const [preserveSelection, setPreserveSelection] = useState(false);

  const handleSummarize = async () => {
    if (!url) {
      setError('Bitte geben Sie eine URL ein');
      return;
    }

    setLoading(true);
    setError('');
    setArticle('');
    setExplanation('');
    setShowExplanation(false);

    try {
      const response = await fetch('/api/summarize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url }),
      });

      if (!response.ok) {
        throw new Error('Fehler beim Zusammenfassen des Artikels');
      }

      const data = await response.json();
      setArticle(data.summary);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Ein Fehler ist aufgetreten');
    } finally {
      setLoading(false);
    }
  };

  const handleTextSelection = () => {
    const selection = window.getSelection();
    if (selection && selection.toString().trim().length > 0) {
      const text = selection.toString().trim();
      console.log("Text ausgewählt:", text); // Debug-Log
      setSelectedText(text);
      
      // Speichere die aktuelle Range
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        setSavedRange(range.cloneRange());
        setPreserveSelection(true);
        
        // Berechne Position für den Erklären-Button
        const rect = range.getBoundingClientRect();
        const articleRect = articleRef.current?.getBoundingClientRect();
        
        if (articleRect) {
          setSelectionPosition({
            top: rect.top - articleRect.top - 40,
            left: rect.left - articleRect.left + (rect.width / 2)
          });
        }
      }
    } else {
      setSelectedText('');
      setSavedRange(null);
      setPreserveSelection(false);
    }
  };

  const handleExplain = async () => {
    if (!selectedText) return;
    
    setExplainLoading(true);
    setShowExplanation(true);
    
    try {
      const response = await fetch('/api/explain', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text: selectedText }),
      });

      if (!response.ok) {
        throw new Error('Fehler beim Erklären des Textes');
      }

      const data = await response.json();
      setExplanation(data.explanation);
    } catch (err) {
      setExplanation('Fehler beim Laden der Erklärung.');
    } finally {
      setExplainLoading(false);
    }
  };

  useEffect(() => {
    if (preserveSelection && savedRange) {
      // Verwende requestAnimationFrame, um nach dem Rendering auszuführen
      const restoreSelection = () => {
        const selection = window.getSelection();
        if (selection) {
          selection.removeAllRanges();
          selection.addRange(savedRange);
        }
        // Erneut planen, um die Auswahl beizubehalten
        if (preserveSelection) {
          requestAnimationFrame(restoreSelection);
        }
      };
      
      requestAnimationFrame(restoreSelection);
    }
    
    return () => {
      setPreserveSelection(false);
    };
  }, [preserveSelection, savedRange]);

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (articleRef.current && !articleRef.current.contains(e.target as Node)) {
        setSelectedText('');
        setShowExplanation(false);
        setSavedRange(null);
        setPreserveSelection(false);
        
        // Entferne die Auswahl
        window.getSelection()?.removeAllRanges();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (savedRange) {
      const selection = window.getSelection();
      if (selection) {
        selection.removeAllRanges();
        selection.addRange(savedRange);
      }
    }
  }, [selectedText, savedRange]);

  return (
    <main className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-gray-900 mb-8 text-center">
          News Summarizer
        </h1>
        
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="flex gap-4">
            <input
              type="url"
              placeholder="URL eingeben..."
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={loading}
            />
            <button
              onClick={handleSummarize}
              disabled={loading}
              className="px-6 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              {loading ? 'Wird zusammengefasst...' : 'Zusammenfassen'}
            </button>
          </div>
          
          {error && (
            <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg text-red-700">
              {error}
            </div>
          )}
        </div>

        {article && (
          <div className="bg-white rounded-lg shadow-md p-8 relative" ref={articleRef}>
            <div 
              className="prose prose-lg max-w-none"
              dangerouslySetInnerHTML={{ __html: article }}
              onMouseUp={handleTextSelection}
            />
            
            {selectedText && (
              <button
                className="absolute bg-blue-600 text-white px-3 py-1 rounded-md text-sm shadow-md hover:bg-blue-700 transition-colors"
                style={{ top: `${selectionPosition.top}px`, left: `${selectionPosition.left}px`, transform: 'translateX(-50%)' }}
                onClick={handleExplain}
              >
                Erklären
              </button>
            )}
            
            {showExplanation && (
              <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h3 className="font-bold text-blue-800 mb-2">Erklärung zu: "{selectedText}"</h3>
                {explainLoading ? (
                  <p className="text-gray-600">Lade Erklärung...</p>
                ) : (
                  <p className="text-gray-800">{explanation}</p>
                )}
                <button 
                  className="mt-2 text-sm text-blue-600 hover:text-blue-800"
                  onClick={() => setShowExplanation(false)}
                >
                  Schließen
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </main>
  );
}
